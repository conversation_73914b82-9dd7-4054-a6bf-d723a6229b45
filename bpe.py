from tokenizers import Tokenizer
from tokenizers.models import BPE
from tokenizers.trainers import BpeTrainer
from tokenizers.pre_tokenizers import Whitespace

# Define your special tokens
special_tokens = ["<sos>", "<eos>", "<unk>", "<sep>"]

# Initialize tokenizer
tokenizer = Tokenizer(BPE(unk_token="<unk>"))
tokenizer.pre_tokenizer = Whitespace()

# Add special tokens to trainer
trainer = BpeTrainer(
    vocab_size=50000,
    special_tokens=special_tokens,
    show_progress=True
)

# Train tokenizer
tokenizer.train(["dataset.txt"], trainer)

# Save tokenizer with special tokens
tokenizer.save("tokenizer.json")
