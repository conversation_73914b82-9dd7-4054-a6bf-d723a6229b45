# # bpe_trainer.py
# import requests
# from tokenizers import Tokenizer, models, trainers, pre_tokenizers

# # 1. Download small text
# url = "https://www.gutenberg.org/cache/epub/1952/pg1952.txt"
# resp = requests.get(url)
# text = resp.text.split('\n\n')[:100]  # first ~50 paragraphs to keep it very small

# # 2. Prepare corpus iterator
# def get_corpus():
#     for block in text:
#         yield block.strip()

# # 3. Setup BPE tokenizer
# tokenizer = Tokenizer(models.BPE())
# tokenizer.pre_tokenizer = pre_tokenizers.Whitespace()

# trainer = trainers.BpeTrainer(
#     vocab_size=1000,     # small vocab
#     special_tokens=["<unk>"]
# )

# # 4. Train
# tokenizer.train_from_iterator(get_corpus(), trainer)

# # 5. Save vocab and merges
# tokenizer.save("tokenizer.json")

# # 6. Test encoding
# encoded = tokenizer.encode("The yellow wall-paper is haunting.")
# print("Tokens:", encoded.tokens)
# print("IDs:", encoded.ids)


import numpy as np

# Generate random integers between 1 and 10 (inclusive)
arr = np.random.randint(1, 11, size=(1, 4096), dtype=np.int32)
arr2 = np.random.randint(1, 11, size=(1, 4096), dtype=np.int32)

# Save as raw binary .bin file
arr.tofile("b1.bin")
arr2.tofile("b2.bin")

