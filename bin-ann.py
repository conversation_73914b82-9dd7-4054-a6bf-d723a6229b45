import random


def logic_op(a, b, gate):
    return (a^b) if gate else not(a^ b)

def hardmax(bits):
    low, high = 0.0, 1.0
    for bit in bits:
        mid = (low + high) / 2
        if bit == 0:
            high = mid
        else:
            low = mid
    return (low + high) / 2


def main():
    input_bits = [random.randint(0, 1) for _ in range(5)]
    connection_matrix = [[random.randint(0, 1) for _ in range(5)] for _ in range(5)]
    hidden_bits = []
    for h in range(5):
        val = 0
        for i in range(5):
            val ^= logic_op(input_bits[i], 1, connection_matrix[i][h])  # logic with fixed 1 for now
        hidden_bits.append(val)
    output_bitstream = hidden_bits
    l=[]
    final_prob = hardmax(output_bitstream)
    l.append(final_prob)
    print(l)
    return max(l)
main()


# hidden layer has 73 nodes and input layer has 4096 nodes  so it generates approx 300k vocabb probab
# then it has to make 73*8 = 584 connections fornearest 2 raised to power n to 300k 8 bits probability vector for lm head
# 

