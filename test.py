import numpy as np
import time

# Parameters
file_path = 'embeddings.bin'
vocab_size = 300_000
packed_dims = 512  # because 4096 bits packed into 512 bytes
full_bits = 4096

# Start timer


# Load packed matrix
data = np.fromfile(file_path, dtype=np.uint8).reshape((vocab_size, packed_dims))

# Extract target row and unpack it (only once)
target = data[0:1, :]  # shape: (1, 512)
target_bits = np.unpackbits(target, axis=1)  # shape: (1, 4096)
start_time = time.time()
# Compute bitwise AND and OR with all rows (on packed data)
and_counts = np.unpackbits(np.bitwise_and(data, target), axis=1).sum(axis=1)
or_counts = np.unpackbits(np.bitwise_or(data, target), axis=1).sum(axis=1)

# Jaccard similarity
jaccard = and_counts / (or_counts + 1e-8)

# Exclude self-match at index 0
jaccard[0] = -1

# Top 5 similar indices
top5_indices = np.argsort(-jaccard)[:5]

# Time taken
elapsed_ms = (time.time() - start_time) * 1000

# Print results
print("Top 5 row IDs:", top5_indices.tolist())
print("Top 5 Jaccard scores:", jaccard[top5_indices].round(5).tolist())
print(f"Time taken: {elapsed_ms:.3f} ms")
