#include <iostream>
#include <fstream>
#include <vector>
#include <queue>
#include <chrono>
#include <cstdint>
#include <cstring>
#include <algorithm>
#include <cstddef>

using namespace std;
using namespace chrono;

// Configurable constants
const int VOCAB_SIZE = 300000;
const int BIT_DIMS = 4096;              // Total bits per embedding
const int WORD_BYTES = BIT_DIMS / 8;    // 512 bytes = 4096 bits
const int WORD_U64 = BIT_DIMS / 64;     // 64 uint64_t per word

// Fast popcount for uint64_t
inline int popcount_u64(uint64_t x) {
#ifdef _MSC_VER
    return static_cast<int>(__popcnt64(x));
#else
    return __builtin_popcountll(x);
#endif
}

// Convert 512 bytes → 64 uint64_t (bit-packed)
void pack_bits(const uint8_t* bytes, uint64_t* out) {
    // Use a simple loop instead of memcpy to avoid include issues
    for (int i = 0; i < WORD_U64; ++i) {
        out[i] = *reinterpret_cast<const uint64_t*>(bytes + i * 8);
    }
}

int main() {
    auto start = high_resolution_clock::now();

    // Load embeddings (512 bytes per word)
    uint8_t* raw_data = new uint8_t[VOCAB_SIZE * WORD_BYTES];
    ifstream file("embeddings.bin", ios::in | ios::binary);
    if (!file) {
        cerr << "Failed to open embeddings.bin\n";
        delete[] raw_data;
        return 1;
    }
    file.read(reinterpret_cast<char*>(raw_data), VOCAB_SIZE * WORD_BYTES);
    file.close();

    // Target vector (first word in vocab)
    uint64_t target[WORD_U64];
    pack_bits(raw_data, target);

    // Min-heap for top-5 (no full sort)
    priority_queue<pair<int, int>, vector<pair<int, int>>, greater<pair<int, int> > > top_k_heap;

    // Compute integer Jaccard for all words
    for (int i = 1; i < VOCAB_SIZE; ++i) {  // skip self-match (row 0)
        uint64_t row[WORD_U64];
        pack_bits(raw_data + i * WORD_BYTES, row);

        // Bitwise AND/OR + popcount
        int and_count = 0, or_count = 0;
        for (int j = 0; j < WORD_U64; ++j) {
            uint64_t a = target[j];
            uint64_t b = row[j];
            and_count += popcount_u64(a & b);
            or_count += popcount_u64(a | b);
        }

        // Integer Jaccard (scaled to 0-256)
        int score = (or_count > 0) ? (and_count * 256) / or_count : 0;

        // Maintain top-5 heap
        if (top_k_heap.size() < 5) {
            top_k_heap.push({score, i});
        } else if (score > top_k_heap.top().first) {
            top_k_heap.pop();
            top_k_heap.push({score, i});
        }
    }

    // Extract top-5 (reverse for descending order)
    pair<int, int> top5[5];
    int count = 0;
    while (!top_k_heap.empty() && count < 5) {
        top5[count] = top_k_heap.top();
        top_k_heap.pop();
        count++;
    }

    // Reverse for descending order
    for (int i = 0; i < count / 2; ++i) {
        pair<int, int> temp = top5[i];
        top5[i] = top5[count - 1 - i];
        top5[count - 1 - i] = temp;
    }

    auto end = high_resolution_clock::now();
    double elapsed_ms = duration<double, milli>(end - start).count();

    // Print results
    cout << "Top 5 row IDs: ";
    for (int i = 0; i < count; ++i) cout << top5[i].second << " ";
    cout << "\nTop 5 Jaccard scores (int): ";
    for (int i = 0; i < count; ++i) cout << top5[i].first << " ";
    cout << "\nTime taken: " << elapsed_ms << " ms\n";

    // Clean up
    delete[] raw_data;
    return 0;
}