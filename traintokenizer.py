import numpy as np
import random
from collections import defaultdict
from tqdm import tqdm
from tokenizers import Tokenizer

# === CONFIG ===
tokenizer_path = "tokenizer.json"
dataset_path = "dataset.txt"
dim = 2048
window = 4
epochs = 20
neg_samples = 5
lr = 0.3

# === Load tokenizer ===
tokenizer = Tokenizer.from_file(tokenizer_path)
vocab_size = tokenizer.get_vocab_size()

# === Load dataset ===
print("[*] Loading dataset...")
with open(dataset_path, "r", encoding="utf-8") as f:
    lines = [line.strip() for line in f if line.strip()]
token_sequences = [tokenizer.encode(line).ids for line in tqdm(lines)]

# === Init embeddings (binary 0/1) ===
E = np.random.randint(0, 2, (vocab_size, dim), dtype=np.uint8)  # token embeddings
C = np.random.randint(0, 2, (vocab_size, dim), dtype=np.uint8)  # context trait vectors

# === Sigmoid ===
def sigmoid(x):
    return 1 / (1 + np.exp(-x))

# === Training loop ===
print("[*] Starting training...")
for epoch in range(epochs):
    loss_total = 0
    for tokens in tqdm(token_sequences, desc=f"Epoch {epoch+1}"):
        for i, target in enumerate(tokens):
            # Sample positive context
            context_ids = tokens[max(0, i-window):i] + tokens[i+1:i+window+1]
            for context in context_ids:
                # Positive pair
                dot_pos = np.dot(E[target], C[context])
                pred_pos = sigmoid(dot_pos)
                loss_pos = -np.log(pred_pos + 1e-9)

                # Negative samples
                loss_neg = 0
                for _ in range(neg_samples):
                    negative = random.randint(0, vocab_size - 1)
                    dot_neg = np.dot(E[target], C[negative])
                    pred_neg = sigmoid(dot_neg)
                    loss_neg += -np.log(1 - pred_neg + 1e-9)

                    # --- Binary bit update (stochastic) for negative ---
                    grad_neg = pred_neg  # derivative of sigmoid for binary
                    for d in range(dim):
                        if C[negative][d] == E[target][d]:
                            if random.random() < lr * grad_neg:
                                E[target][d] ^= 1  # Flip bit
                                C[negative][d] ^= 1

                # --- Binary bit update for positive ---
                grad_pos = 1 - pred_pos  # pull toward agreement
                for d in range(dim):
                    if C[context][d] != E[target][d]:
                        if random.random() < lr * grad_pos:
                            E[target][d] ^= 1
                            C[context][d] ^= 1

                loss_total += loss_pos + loss_neg

    print(f"[Epoch {epoch+1}] Loss: {loss_total:.2f}")

# === Save final binary embedding matrix ===
print("[*] Saving final embeddings...")
final_bits = np.packbits(E, axis=1)
final_bits.tofile("embeddings.bin")
print("[✓] Done: embeddings.bin")
