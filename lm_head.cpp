#include <iostream>
#include <vector>
#include <random>
#include <chrono>
#include <algorithm>
#include <iomanip>
#include <numeric>
#include <cstddef>

using namespace std;
using namespace chrono;

int logic_op(int a, int b, int gate) {
    return gate ? (a ^ b) : !(a ^ b);
}

double hardmax(const int* bits, int size) {
    double low = 0.0, high = 1.0;
    for (int i = 0; i < size; ++i) {
        double mid = (low + high) / 2.0;
        if (bits[i] == 0)
            high = mid;
        else
            low = mid;
    }
    return (low + high) / 2.0;
}

void bin_ann(int* top_indices, double* top_probs) {
    const int input_size = 4096;
    const int hidden_size = 73;

    random_device rd;
    mt19937 gen(rd());
    uniform_int_distribution<> dist(0, 1);

    // 1. Random binary input
    int* input_bits = new int[input_size];
    for (int i = 0; i < input_size; ++i)
        input_bits[i] = dist(gen);

    // 2. Random connection matrix
    int** connection_matrix = new int*[input_size];
    for (int i = 0; i < input_size; ++i) {
        connection_matrix[i] = new int[hidden_size];
        for (int j = 0; j < hidden_size; ++j)
            connection_matrix[i][j] = dist(gen);
    }

    // 3. Hidden bits
    int* hidden_bits = new int[hidden_size];
    for (int h = 0; h < hidden_size; ++h) {
        int val = 0;
        for (int i = 0; i < input_size; ++i)
            val ^= logic_op(input_bits[i], 1, connection_matrix[i][h]);
        hidden_bits[h] = val;
    }

    // 4. Output bitstream
    const int output_size = input_size * hidden_size;
    int* output_bitstream = new int[output_size];
    int idx = 0;
    for (int i = 0; i < input_size; ++i)
        for (int h = 0; h < hidden_size; ++h)
            output_bitstream[idx++] = logic_op(input_bits[i], hidden_bits[h], connection_matrix[i][h]);

    // 5. Group into bytes & apply hardmax
    const int num_bytes = output_size / 8;
    double* probs = new double[num_bytes];
    for (int i = 0; i < num_bytes; ++i) {
        int byte_bits[8];
        for (int j = 0; j < 8; ++j)
            byte_bits[j] = output_bitstream[i * 8 + j];
        probs[i] = hardmax(byte_bits, 8);
    }

    // 6. Get top 5 indices using simple selection sort
    int* indices = new int[num_bytes];
    for (int i = 0; i < num_bytes; ++i)
        indices[i] = i;

    // Sort first 5 elements by probability (descending)
    for (int i = 0; i < 5 && i < num_bytes; ++i) {
        int max_idx = i;
        for (int j = i + 1; j < num_bytes; ++j) {
            if (probs[indices[j]] > probs[indices[max_idx]])
                max_idx = j;
        }
        // Swap
        int temp = indices[i];
        indices[i] = indices[max_idx];
        indices[max_idx] = temp;
    }

    // Copy results
    for (int i = 0; i < 5 && i < num_bytes; ++i) {
        top_indices[i] = indices[i];
        top_probs[i] = probs[indices[i]];
    }

    // Clean up
    delete[] input_bits;
    for (int i = 0; i < input_size; ++i)
        delete[] connection_matrix[i];
    delete[] connection_matrix;
    delete[] hidden_bits;
    delete[] output_bitstream;
    delete[] probs;
    delete[] indices;
}

int main() {
    auto start = high_resolution_clock::now();

    int top_indices[5];
    double top_probs[5];
    bin_ann(top_indices, top_probs);

    cout << "Top indices: ";
    for (int i = 0; i < 5; ++i)
        cout << top_indices[i] << " ";
    cout << "\nTop probabilities: ";
    for (int i = 0; i < 5; ++i)
        cout << fixed << setprecision(9) << top_probs[i] << " ";
    cout << endl;

    auto end = high_resolution_clock::now();
    auto duration = duration_cast<microseconds>(end - start);
    cout << "Time taken: " << duration.count() / 1000.0 << " ms" << endl;

    return 0;
}
