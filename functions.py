import numpy as np
def bin_file(filename: str, rows: int, cols: int):
    if cols % 8 != 0:
        raise ValueError("Column size must be divisible by 8 for bit-packing.")

    matrix = np.random.randint(0, 2, size=(rows, cols), dtype=np.uint8)

    # Pack bits: each row becomes cols // 8 bytes
    packed_matrix = np.packbits(matrix, axis=1)

    packed_matrix.tofile(filename)

    print(f"Bit-packed binary matrix saved to '{filename}' with shape {packed_matrix.shape} (original: {rows}x{cols}).")



def hardmax(bits):
    low, high = 0.0, 1.0
    for bit in bits:
        mid = (low + high) / 2
        if bit == 0:
            high = mid
        else:
            low = mid
    return (low + high) / 2

print(hardmax([0, 1, 0, 1 ]))





def change(a,b,op): return a ^ b if op else ~(a ^ b) & 1

# plan is ki generate a jaccard sim for the very last token with respect to all other tokens (kinda masking)
# then u have [0.53,0.45,0.23,0.12...]
# then use knn to genrate all helping words (a predifined dictonary based on sliding window training)
# so then predic the next word with most probablity (pre defined softmax)

