import torch
import torch.nn as nn
import json
import numpy as np
from tqdm import tqdm  # Added for progress bars

# 1. Load tokenizer
with open("tokenizer.json", encoding='utf-8') as f:
    tokenizer = json.load(f)
    token_to_id = {token: idx for idx, token in enumerate(tokenizer['model']['vocab'])}
V = len(token_to_id)

# 2. Optimized Semantic Bit Probes Model
class SemanticBitProbes(nn.Module):
    def __init__(self, vocab_size, dim=2048):
        super().__init__()
        self.probes = nn.Parameter(torch.randn(vocab_size, dim) * 0.01)
        self.temperature = 10.0  # Start high for smooth gradients
    
    def get_binary(self):
        """Convert to HARD BINARY embeddings (0/1)"""
        return (torch.sigmoid(self.probes * self.temperature) > 0.5).float()
    
    def forward(self, target_ids, context_ids, num_negatives=5):
        """Skip-gram with negative sampling - optimized for speed"""
        batch_size = len(target_ids)
        
        # Get continuous probes (gradients flow here)
        target_probe = torch.sigmoid(self.probes[target_ids] * self.temperature)
        pos_probe = torch.sigmoid(self.probes[context_ids] * self.temperature)
        
        # Positive loss: maximize dot product
        pos_sim = torch.einsum('bd,bd->b', target_probe, pos_probe)
        pos_loss = -torch.log(torch.sigmoid(pos_sim) + 1e-8)
        
        # Negative sampling (vectorized for speed)
        neg_ids = torch.randint(0, V, (batch_size * num_negatives,), device=target_ids.device)
        neg_probe = torch.sigmoid(self.probes[neg_ids] * self.temperature)
        neg_probe = neg_probe.view(batch_size, num_negatives, -1)
        
        neg_sim = torch.einsum('bd,bnd->bn', target_probe, neg_probe)
        neg_loss = -torch.sum(torch.log(torch.sigmoid(-neg_sim) + 1e-8), dim=1)
        
        return torch.mean(pos_loss + neg_loss)

# 3. MEMORY-EFFICIENT Corpus Processing
def build_corpus():
    """Convert dataset.txt to token IDs with streaming processing"""
    corpus_ids = []
    token_count = 0
    
    with open("dataset.txt", encoding="utf-8") as f:
        for line in tqdm(f, desc="Loading corpus"):
            tokens = line.lower().split()
            token_count += len(tokens)
            for token in tokens:
                if token in token_to_id:
                    corpus_ids.append(token_to_id[token])
    
    print(f"\nCorpus loaded: {len(corpus_ids):,} tokens ({token_count:,} total)")
    return corpus_ids

def generate_batches(corpus_ids, batch_size=128, window_size=5):
    """Generate batches WITHOUT precomputing all pairs (memory efficient)"""
    total_pairs = 0
    for i in range(len(corpus_ids)):
        start = max(0, i - window_size)
        end = min(len(corpus_ids), i + window_size + 1)
        total_pairs += (end - start - 1)  # Skip self
    
    # Yield batches on-the-fly
    indices = np.arange(len(corpus_ids))
    np.random.shuffle(indices)
    
    for i in tqdm(range(0, len(indices), batch_size), 
                 desc="Generating batches", 
                 total=len(indices)//batch_size):
        batch_indices = indices[i:i+batch_size]
        targets, contexts = [], []
        
        for idx in batch_indices:
            start = max(0, idx - window_size)
            end = min(len(corpus_ids), idx + window_size + 1)
            for j in range(start, end):
                if idx != j:  # Skip self
                    targets.append(corpus_ids[idx])
                    contexts.append(corpus_ids[j])
        
        if targets:  # Skip empty batches
            yield torch.tensor(targets), torch.tensor(contexts)

# 4. TRAINING WITH PROGRESS BARS
corpus_ids = build_corpus()
model = SemanticBitProbes(V, dim=2048)
optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

# Precompute total batches for tqdm
total_batches = (len(corpus_ids) * (2 * 5)) // 128  # Approx for window=5

for epoch in range(50):
    model.temperature = max(1.0, 10.0 * 0.95**epoch)
    epoch_loss = 0
    batch_count = 0
    
    # Main training progress bar
    pbar = tqdm(generate_batches(corpus_ids), 
                total=total_batches,
                desc=f"Epoch {epoch+1:2d} [T={model.temperature:.1f}]")
    
    for targets, contexts in pbar:
        loss = model(targets, contexts)
        loss.backward()
        optimizer.step()
        optimizer.zero_grad()
        
        epoch_loss += loss.item()
        batch_count += 1
        
        # Update progress bar with current loss
        pbar.set_postfix(loss=f"{loss.item():.4f}")
    
    # Final epoch summary
    avg_loss = epoch_loss / batch_count if batch_count else float('inf')
    tqdm.write(f"Epoch {epoch+1:2d} | Avg Loss: {avg_loss:.4f} | Temp: {model.temperature:.2f}")

# 5. SAVE RESULTS
binary_embeddings = model.get_binary()

# Verify embedding (using first token in vocab)
sample_token = list(token_to_id.keys())[0]
sample_id = token_to_id[sample_token]
print(f"\nFirst 32 bits of '{sample_token}':")
print("".join(map(str, binary_embeddings[sample_id].int().tolist()[:32])))

# Save embeddings
torch.save(binary_embeddings, "binary_embeddings.pt")
print("\n✅ Binary embeddings saved to binary_embeddings.pt")
print(f"Shape: {binary_embeddings.shape} (vocab_size x 2048)")