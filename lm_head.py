import random
import numpy as np

def logic_op(a, b, gate):
    return (a ^ b) if gate else int(not (a ^ b))

# Hardmax converts bitstream to probability using binary range narrowing
def hardmax(bits):
    low, high = 0.0, 1.0
    for bit in bits:
        mid = (low + high) / 2
        if bit == 0:
            high = mid
        else:
            low = mid
    return (low + high) / 2

# Core Bin-ANN logic
def bin_ann():
    input_size = 4096
    hidden_size = 73

    # 1. Random binary input
    input_bits = [random.randint(0, 1) for _ in range(input_size)]

    # 2. Random binary connection matrix [input x hidden], each 0 (XNOR) or 1 (XOR)
    connection_matrix = [[random.randint(0, 1) for _ in range(hidden_size)] for _ in range(input_size)]

    # 3. Compute hidden bits: each hidden node is XOR/XNOR of all inputs based on connection gates
    hidden_bits = []
    for h in range(hidden_size):
        val = 0
        for i in range(input_size):
            val ^= logic_op(input_bits[i], 1, connection_matrix[i][h])
        hidden_bits.append(val)

    # 4. Generate output bitstream: For each input node, apply logic with each hidden node → get input_size * hidden_size bits
    output_bitstream = []
    for i in range(input_size):
        for h in range(hidden_size):
            gate = connection_matrix[i][h]
            output_bitstream.append(logic_op(input_bits[i], hidden_bits[h], gate))

    # 5. Group bits into bytes (8 bits per vocab), compute probability using hardmax
    byte_chunks = [output_bitstream[i:i+8] for i in range(0, len(output_bitstream), 8)]

    # Convert to probabilities
    probs = [hardmax(chunk) for chunk in byte_chunks if len(chunk) == 8]

    # Get top 5 token indices by probability
    top_5_indices = np.argsort(probs)[-5:][::-1]
    top_5_probs = [probs[i] for i in top_5_indices]

    return top_5_indices.tolist(), top_5_probs

# Run once and get result
from time import time
a = time()
top_indices, top_probs = bin_ann()
print(top_indices)
print(top_probs)
print(f"Time taken: {(time() - a) * 1000:.2f} ms")

