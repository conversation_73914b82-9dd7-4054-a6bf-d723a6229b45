
import numpy as np
import json
from numba import njit


#================================== BIN FILE CREATEION ===================================

def bin_file(filename: str, rows: int, cols: int):
    if cols % 8 != 0:
        raise ValueError("Column size must be divisible by 8 for bit-packing.")

    matrix = np.random.randint(0, 2, size=(rows, cols), dtype=np.uint8)

    # Pack bits: each row becomes cols // 8 bytes
    packed_matrix = np.packbits(matrix, axis=1)

    packed_matrix.tofile(filename)

    print(f"Bit-packed binary matrix saved to '{filename}' with shape {packed_matrix.shape} (original: {rows}x{cols}).")




#================================== HARDMAX ===================================


def hardmax(bits):
    low, high = 0.0, 1.0
    for bit in bits:
        mid = (low + high) / 2
        if bit == 0:
            high = mid
        else:
            low = mid
    return (low + high) / 2




#================================== LM_HEAD ===================================




def read_weight_matrix(bin_file_path):
    # Read the .bin file (uint8, bit-packed) and unpack into a 584x4096 binary matrix
    with open(bin_file_path, 'rb') as f:
        data = np.fromfile(f, dtype=np.uint8)
    # Unpack uint8 to bits (each byte gives 8 bits)
    bits = np.unpackbits(data).reshape(584, 4096)
    return bits

def lm_head(groups, node_matrix, weight_matrix_path, input_bitstream):
    # node_matrix: 1x584 array of values (bits or numbers)
    # weight_matrix_path: Path to .bin file (584x4096, uint8 bit-packed)
    # input_bitstream: uint8 bit-packed array, at least 4096 bits
    # groups: Number of bits per group (e.g., 8)

    # Validate inputs
    if node_matrix.shape != (1, 584):
        raise ValueError("node_matrix must be 1x584")
    if groups <= 0 or 4096 % groups != 0:
        raise ValueError("groups must divide 4096 evenly")

    # Read and unpack weight matrix
    weight_matrix = read_weight_matrix(weight_matrix_path)  # Shape: (584, 4096)
    
    # Unpack input bitstream (uint8) to bits
    input_bits = np.unpackbits(input_bitstream)[:4096]  # Take first 4096 bits
    
    # Ensure node_matrix is treated as binary (0 or 1) for XOR/XNOR
    node_bits = (node_matrix[0] > 0).astype(np.uint8)  # Convert to binary if needed

    results = []
    for i in range(584):  # For each row of weight matrix (and node value)
        node_value = node_bits[i]  # Current node value
        # Compute operations for the i-th row
        result_bits = np.zeros(4096, dtype=np.uint8)
        for j in range(4096):
            if weight_matrix[i, j] == 1:
                # XOR: 1 if inputs differ, 0 if same
                result_bits[j] = node_value ^ input_bits[j]
            else:
                # XNOR: 1 if inputs are same, 0 if differ
                result_bits[j] = 1 - (node_value ^ input_bits[j])
        
        # Group bits into chunks of 'groups' bits
        grouped_bits = result_bits.reshape(-1, groups)  # Shape: (4096/groups, groups)
        probabilities = []
        for k, group in enumerate(grouped_bits):
            # Apply hardmax to each group
            prob = hardmax(group)
            probabilities.append((k, prob))  # Store (token_index, probability)
        
        # Get top 3 probabilities without sorting the entire list
        top_3 = sorted(probabilities, key=lambda x: x[1], reverse=True)[:1]
        results.append(top_3)
    
    # Return results: list of 584 lists, each with top 3 (token_index, probability) tuples
    return results



#================================== MLP ===================================




_popcount_table = np.fromiter((bin(i).count("1") for i in range(256)), dtype=np.uint8)

@njit
def set_bit_in_bytes(arr_bytes, bit_index, val):
    byte_idx = bit_index // 8
    bit_in_byte = 7 - (bit_index % 8)
    if val:
        arr_bytes[byte_idx] |= (1 << bit_in_byte)

@njit
def process_layer(input_bytes, weights, node_bits, bias, out_len):
    cols_bytes = input_bytes.size
    out_packed = np.zeros((out_len + 7) // 8, dtype=np.uint8)
    for r in range(out_len):
        node_bit = node_bits[0, r]        # ✅ row-vector indexing
        w_row = weights[r]
        if node_bit:
            base = np.bitwise_xor(input_bytes, 0xFF)
        else:
            base = input_bytes
        mask = np.bitwise_xor(w_row, 0xFF)
        result = np.bitwise_xor(base, mask)
        popcnt = 0
        for b in result:
            popcnt += _popcount_table[b]
        if popcnt > bias[r]:
            set_bit_in_bytes(out_packed, r, 1)
    return out_packed

def load_packed_rows(path, cols_bits):
    data = np.fromfile(path, dtype=np.uint8)
    cols_bytes = (cols_bits + 7) // 8
    rows = data.size // cols_bytes
    return data.reshape(rows, cols_bytes)

def load_node_bits(path, rows):
    b = np.fromfile(path, dtype=np.uint8)
    bits = np.unpackbits(b, bitorder='big')[:rows]
    return bits.astype(np.uint8).reshape(1, rows)   # ✅ always (1, rows)

def load_bias_infer(path, rows):
    for dt in (np.int32, np.int16, np.int8):
        arr = np.fromfile(path, dtype=dt)
        if arr.size == rows:
            return arr.astype(np.int32)
    raise ValueError("bias file length mismatch")

def mlp(input_packed_uint8, w1_path, n1_path, b1_path, w2_path, n2_path, b2_path):
    input_bytes = np.asarray(input_packed_uint8, dtype=np.uint8)
    input_bits = input_bytes.size * 8

    # --- Layer 1 ---
    weights1 = load_packed_rows(w1_path, input_bits)
    rows1 = weights1.shape[0]
    node1_bits = load_node_bits(n1_path, rows1)   # shape (1, rows1)
    bias1 = load_bias_infer(b1_path, rows1)
    hidden_packed = process_layer(input_bytes, weights1, node1_bits, bias1, rows1)

    # --- Layer 2 ---
    weights2 = load_packed_rows(w2_path, rows1)
    rows2 = weights2.shape[0]
    node2_bits = load_node_bits(n2_path, rows2)   # shape (1, rows2)
    bias2 = load_bias_infer(b2_path, rows2)
    output_packed = process_layer(hidden_packed, weights2, node2_bits, bias2, rows2)

    return output_packed



# ================================== GET SHAPE ===================================

import numpy as np
import os

def shape(path):
    """
    Inspect a binary weight matrix file and return its shape in (rows, cols_bits).
    Assumes the file stores a 2D matrix in row-major order, bit-packed.
    """
    data = np.fromfile(path, dtype=np.uint8)

    # Total bits in file
    total_bits = data.size * 8  

    # At this point we don’t know rows vs cols split.
    # If you expect each row is stored contiguously, you need either:
    #  - Metadata (rows or cols), or
    #  - Convention (e.g., vocab_size is known).
    
    return {
        "file_size_bytes": data.size,
        "file_size_bits": total_bits,
        "unknown_shape": True,
        "hint": "Need either rows or cols to deduce shape."
    }

# ================================== TOKENIZER ===================================


from tokenizers import Tokenizer
tokenizer = Tokenizer.from_file("tokenizer.json")

# tokenizer.encode("string sentence")
# tokenizer.decode([token_ids])



# ================================== EMBEDDINGS ===================================


def load_packed_embeddings(path, cols_bytes):
    data = np.fromfile(path, dtype=np.uint8)
    rows = data.size // cols_bytes
    return data.reshape(rows, cols_bytes)

def embedd(token_ids, path, cols_bytes):
    # Load once
    embedding_matrix = load_packed_embeddings(path, cols_bytes)
    # Lookup
    return embedding_matrix[np.array(token_ids, dtype=np.int32)]


# ================================== ATTENTION ===================================



def attention(rows):
    rows = np.asarray(rows, dtype=bool)
    if rows.ndim != 2:
        raise ValueError("Expected 2D array")
    if len(rows) < 1:
        raise ValueError("Need at least one token")

    if len(rows) == 1:
        c1 = np.ones(rows.shape[1], dtype=bool)
        c2 = np.zeros(rows.shape[1], dtype=bool)
    else:
        c1 = rows[0].copy()
        c2 = rows[0].copy()
        for r in rows[1:-1]:
            c1 &= r
            c2 |= r

    v = rows[-1].copy()
    mask_common = c1
    mask_contextual = c2 & (~c1)
    v_prime = (v & mask_common) | ((v | mask_contextual) & mask_contextual)
    v_final = v_prime & c2
    return v_final.astype(np.uint8), c1, c2

def auto_attention(v, c1, c2):
    v = np.asarray(v, dtype=bool)
    mask_common = c1
    mask_contextual = c2 & (~c1)
    v_prime = (v & mask_common) | ((v | mask_contextual) & mask_contextual)
    v_final = v_prime & c2
    c1 = c1 & v
    c2 = c2 | v
    return v_final.astype(np.uint8), c1, c2






######################## BITFORMER ########################


class Bitformer:
    def __init__(self, config_path: str):
        with open(config_path, "r") as f:
            self.config = json.load(f)
    def tokenize(self, string: str):
        return tokenizer.encode(string).ids
    
    def detokenize(self, token_ids: list):
        return tokenizer.decode(token_ids)

    def forward(self, input_string: str):
        input_ids = self.tokenize(input_string)
        embeddings = embedd(input_ids, "embedding_matrix.bin",512)
        attention_op = attention(embeddings)
        mlp_op = mlp(attention_op, "w1.bin", "n1.bin", "b1.bin", "w2.bin", "n2.bin", "b2.bin")
        lm_head_op = lm_head(8, mlp_op, "lm_head_weight.bin", input_ids)
        return lm_head_op





# usgae
SLM = Bitformer("config.json")
print(SLM.forward("The ship is sailing"))

